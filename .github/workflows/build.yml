name: Build App
on:
  push:
    branches: [main, test]
  pull_request:
    branches: [main, test]

jobs:
  build-app:
    runs-on: ubuntu-latest

    services:
      mariadb:
        image: mariadb:10.6 # TODO: Update this to 11.4.4 when it becomes available on Github Actions
        env:
          MARIADB_ROOT_PASSWORD: laravel
          MARIADB_DATABASE: laravel
          MARIADB_USER: laravel
          MARIADB_PASSWORD: secret
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'

      - name: Install Composer Dependencies
        run: composer install --no-progress --no-suggest

      - name: Install NPM Dependencies
        run: npm install --pure-lockfile && npm run build

      - name: Set Application Key
        run: cp .env.example .env && php artisan key:generate

      - name: Run Database Migrations
        run: php artisan migrate --force -vvv

      - name: Run Database Seeding
        run: php artisan db:seed --force -vvv

      # - name: Run PHP Code style checks
      #   run: composer lint

      # - name: Run PHPUnit Tests
      #   run: php artisan test
