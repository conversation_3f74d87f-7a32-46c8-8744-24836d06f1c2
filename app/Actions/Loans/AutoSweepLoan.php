<?php

namespace App\Actions\Loans;

use App\Models\Loan;
use App\Models\LoanSweep;

class AutoSweepLoan
{
    protected ?string $provider;

    public function execute(Loan $loan)
    {
        $loan->load(['customer', 'schedule', 'loan_product']);

        return LoanSweep::query()->updateOrCreate(
            [
                'partner_id' => $loan->Partner_ID,
                'loan_id' => $loan->id,
                'customer_id' => $loan->Customer_ID,
                'amount' => $loan->totalOutstandingBalance(),
            ],
            [
                'partner_id' => $loan->Partner_ID,
                'loan_id' => $loan->id,
                'customer_id' => $loan->Customer_ID,
                'amount' => $loan->totalOutstandingBalance(),
                'provider_product' => 'RE_COLLECT',
                'operation_type' => 'MANDATE_AND_COLLECT',
                'loan_product_code' => $loan->loan_product->Code,
            ]
        );
    }

    public function useProvider(string $provider)
    {
        $this->provider = $provider;

        return $this;
    }
}
