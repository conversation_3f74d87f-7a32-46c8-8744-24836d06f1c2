<?php

namespace App\Actions\Loans;

use App\Enums\LoanAccountType;
use App\Models\JournalEntry;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\LoanSchedule;
use App\Models\Transaction;
use App\Services\MtnApiService;
use App\Services\PaymentServiceManager;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\ModelStatus\Exceptions\InvalidStatus;

class ProcessMtnAutoSweepAction
{
    /**
     * @throws Exception
     */
    public function execute(Loan $loan): Loan
    {
        try {
            DB::transaction(function () use ($loan) {
                $transaction = Transaction::query()->where('Loan_Application_ID', $loan->Loan_Application_ID)->latest()->first();

                if (empty($transaction)) {
                    throw new Exception('Transaction not found');
                }

                $api = (new PaymentServiceManager($transaction))->paymentService;
                $result = $api->closeLoan($transaction);

                if (! $result) {
                    throw  new Exception('Error while closing loan');
                }
            });

            return $loan->fresh();
        } catch (\Throwable $th) {
            throw new Exception($th->getMessage());
        }
    }
}
