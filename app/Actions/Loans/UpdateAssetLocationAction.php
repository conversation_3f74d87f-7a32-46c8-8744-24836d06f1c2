<?php

namespace App\Actions\Loans;

use Illuminate\Support\Str;
use App\Models\CustomerAsset;
use App\Models\LoanApplication;
use App\Services\SpiroApiService;
use App\Events\LoanApplicationApproved;

class UpdateAssetLocationAction
{
    public function execute($identification, $loanId)
    {
        $customerAsset = CustomerAsset::query()
            ->where('identification', $identification)
            ->where('Loan_ID', $loanId)->first();

        if (empty($customerAsset)) {
            return;
        }

        $bikeDetails = (new SpiroApiService())->getBikeDetails($identification);

        if (empty($bikeDetails)) {
            return;
        }

        return $customerAsset->update([
            'Latitude' => $bikeDetails['location_latitude'],
            'Longitude' => $bikeDetails['location_longitude'],
        ]);
    }
}
