<?php

namespace App\Console\Commands\AirtelTests;

use App\Actions\CreateTestTransactionAction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

class Disburse extends Command
{
  protected $signature = 'airtel:disburse {--phone=} {--amount=} {--partner=}';

  protected $description = 'Disburse money to customer';

  public function handle(): int
  {
    try {
      $transaction = app(CreateTestTransactionAction::class)->execute(
        $this->option('amount'),
        $this->option('phone'),
        $this->option('partner'),
      );
      $api = (new PaymentServiceManager($transaction))->paymentService;
      $response = $api->disburse(
        $transaction->Telephone_Number,
        $transaction->Amount,
        $transaction->TXN_ID,
        $transaction->Type
      );

      $this->info(json_encode($response, true));

      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
