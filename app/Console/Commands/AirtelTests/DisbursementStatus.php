<?php

namespace App\Console\Commands\AirtelTests;

use App\Actions\CreateTestTransactionAction;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

class DisbursementStatus extends Command
{
  protected $signature = 'airtel:disburse-status {id}';

  protected $description = 'Get status of a disbursement to customer';

  public function handle(): int
  {
    try {
        $transaction = Transaction::query()->firstWhere('TXN_ID', $this->argument('id'));

        if (empty($transaction)) {
            $this->error('Transaction not found: '.$this->option('id'));

            return 1;
        }

      $api = (new PaymentServiceManager($transaction))->paymentService;
      $response = $api->disbursementStatus($transaction->TXN_ID);

      $this->info(json_encode($response, true));

      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
