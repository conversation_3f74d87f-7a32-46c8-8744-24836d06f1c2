<?php

namespace App\Console\Commands;

use App\Jobs\CheckAssetStatus;
use App\Models\CustomerAsset;
use App\Models\Transaction;
use App\Services\LoanDefaulterManager;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class HandleAssetLoanDefaulters extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:defaulters {--days=8} {--partner=} {--lp=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Find loan defaulters and forward them for action';

    /**
     * Execute the console command.
     */
    public function handle()
    {
      /**
       * todo: Create a UI where immobilization rules can be set. This way, we can be dynamic in finding defaulters who meet those rules.
       * Currently we are are only supporting DFCU to handle defaulters
       */
      $manager = app(LoanDefaulterManager::class);
      $manager->forPartner($this->option('partner'))
        ->onLoanProduct($this->option('lp'))
        ->forDays($this->option('days'));
    }
}
