<?php

namespace App\Helpers;

class SystemResource
{

    public static function getSystemResources(): array
    {
        return [
            "dashboard",
            "customers",
            "switches",
            "partners",
            "downpayments",
            "decision-engine",
            "exclusion-parameters",
            "business-rules",
            "loans",
            "loan-applications",
            "loan-accounts",
            "loan-products",
            "loan-product-terms",
            "taxes",
            "due-loans-report",
            "missed-repayments-report",
            "no-repayments",
            "past-maturity-date",
            "principal-outstanding",
            "1-month-late-loans",
            "3-months-late-loans",
            "savings",
            "savings-product",
            "savings-accounts",
            "savings-deposits",
            "savings-preferences",
            "savings-providers",
            "savings-applications",
            "savings-withdraws",
            "sms",
            "sms-templates",
            "sms-campaigns",
            "sms-logs",
            "float-management",
            "sms-float-topups",
            "loan-report",
            "disbursement-report",
            "pending-disbursement-report",
            "collections-report",
            "loans-in-arrears-report",
            "paidoff-report",
            "outstanding-report",
            "overdue-report",
            "blacklisted-report",
            "repayment-report",
            "written-report",
            "written-off-recovered-report",
            "portfolio-at-risk-report",
            "arrears-aging-report",
            "monthly-report",
            "rejected-loans-report",
            "loan-product-report",
            "savings-report",
            "savings-summary-report",
            "financial-reports",
            "chart-of-accounts",
            "trial-balance-report",
            "balance-sheet-report",
            "income-statement-report",
            "general-ledger-summary",
            "cash-flow-statement",
            "income-report",
            "transactions",
            "daily-transactions-report",
            "gl-statement-report",
            "other-reports",
            "borrowers-report",
            "fees-report",
            "daily-report",
            "at-a-glance-report",
            "deferred-income-report",
            "deferred-monthly-income-report",
            "payment-history-velocity-report",
            "users",
            "roles",
            "audit-trail",
            "loan-product-terms",
            "savings-reports",
            "hits",
            "asset-locations",
            "cash-sales",
            "asset-management",
            "loan-assets",
            "loan-applications-report",
            "loan-loss-provisions",
            "tickets",
            "labels",
            "categories",
            "agents",
            "ticket-dashboard",
            "ticket-reports",
        ];
    }
}
