<?php

namespace App\Http\Controllers;

use Exception;
use App\Models\Loan;
use App\Models\Partner;
use App\Models\AssetFee;
use App\Models\Customer;
use App\Helpers\AssetLoans;
use App\Models\LoanProduct;
use App\Models\Transaction;
use Illuminate\Support\Str;
use App\Models\LoanSchedule;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use App\Models\LoanApplication;
use App\Models\LoanProductTerm;
use App\Models\LoanProductType;
use App\Models\Accounts\Account;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Events\LoanApplicationApproved;
use App\Events\LoanApplicationRejected;
use Illuminate\Support\Facades\Storage;
use App\Services\Account\AccountSeederService;
use App\Actions\Loans\ApproveLoanApplication;
use App\Models\UssdHit;

class LoanApplicationController extends Controller
{
    // todo: Rename to approve
    public function disburseAssetLoan(Request $request, ApproveLoanApplication $action, $transactionID)
    {
        try {
            $transaction = Transaction::where('TXN_ID', $transactionID)
                ->whereNull('Loan_ID')
                ->first();

            if (!$transaction) {
                throw new Exception('Transaction not found');
            }

            if (strtolower($transaction->Asset_Disbursement_Status) !== 'submitted') {
                throw new Exception('Documents have not been submitted yet');
            }
            $action->execute($transaction->loanApplication, $request);
            $application = $transaction->loanApplication;
            // AssetLoans::affectDisbursementAccounts($application);
            session()->flash("success", "Loan disbursed successfully");
            return redirect()->route('loan-applications.show', compact('application'));
        } catch (Exception $e) {
            session()->flash("error", $e->getMessage());
            return back();
        }
    }


    public function index(Request $request)
    {
        return view('loan-applications.index');
    }

    public function create()
    {
        return view('loan-applications.create');
    }

    public function generateLoanSummary(Request $request, Customer $customer)
    {
        $request->validate([

            //"Partner_ID" => "required|integer",
            //"Customer_ID" => "required|integer", needed
            "Loan_Product_ID" => "required|integer",
            "Loan_Purpose" => "required|string",
            "Credit_Application_Date" => "required|date",
            "Amount" => "required|integer",
            //"Amount" => "required|decimal",
            //"Credit_Account_or_Loan_Product_Type" => "required|integer",
            //"Credit_Application_Duration" => "required|integer"
        ]);

        try {
            $loan_amount = $request->Amount;
            $loan_product_id = $request->Loan_Product_ID;
            $loan_purpose = $request->Loan_Purpose;
            $application_date = $request->Credit_Application_Date;
            //$loanTermInDays = $request->loanTermInDays;
            $loanProduct = LoanProduct::where('id', $loan_product_id)->first();
            $frequencyOfInstallmentRepayment = "Monthly"; //pick freq dynamically
            $loanSummaryDetails = [];
            $loanData = [];
            $loanRecordDetails = [];

            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loan = $customer->loans()
                ->whereNot('Credit_Account_Status', 4) // Fully Paid
                ->whereNot('Credit_Account_Status', 3) // Written-off
                ->latest()
                ->first();

            if ($loan) {
                throw new Exception('Customer already has an active loan.', 400);
            }

            $loanApplicationSummary = new LoanApplication();

            //dd($application_date);

            $loanSummaryDetails = $loanApplicationSummary->generateLoanSummaryDetails($customer, $loan_amount, $loan_purpose, $loan_product_id, $application_date);

            //dd($loanSummaryDetails);

            $loanData = $loanSummaryDetails['loanData'];
            $loanRecordDetails = $loanSummaryDetails['loanRecordDetails'];
        } catch (\Throwable $th) {
            return $th;
        }

        return view('loan-applications.summary', compact('customer', 'loanRecordDetails', 'loanData'));
    }

    public function store(Request $request)
    {

        $request->validate([

            //"Partner_ID" => "required|integer",
            "Customer_ID" => "required|integer",
            "Loan_Product_ID" => "required|integer",
            "Loan_Purpose" => "required|string",
            "Credit_Application_Date" => "required|date",
            "Amount" => "required|integer",
            //"Amount" => "required|decimal",
            //"Credit_Account_or_Loan_Product_Type" => "required|integer",
            //"Credit_Application_Duration" => "required|integer"
        ]);

        try {

            $loan_application = new LoanApplication();
            $loan_application->Request_ID = Str::uuid()->toString();
            $loan_application->Partner_ID = Auth::user()->partner_id;
            $loan_application->Customer_ID = $request->Customer_ID;
            $loan_application->Loan_Product_ID = $request->Loan_Product_ID;
            $loan_application->Loan_Purpose = $request->Loan_Purpose;
            $loan_application->Applicant_Classification = "Individual";
            $loan_application->Credit_Application_Date = $request->Credit_Application_Date;
            $loan_application->Amount = $request->Amount;
            //$loan_application->Currency = "UGX";
            $loan_application->Credit_Application_Status = "Pending";
            //$loan_application->Last_Status_Change_Date = Carbon::createFromFormat(Y-m-d; H:i; $request->Credit_Application_Date)->toDateTimeString();
            $loan_application->Last_Status_Change_Date = Carbon::now();
            $loan_application->Credit_Application_Duration = 0;
            $loan_application->Client_Consent_flag = "Yes";
            $loan_application->Credit_Amount_Approved = 0;
            $loan_application->Credit_Account_or_Loan_Product_Type = 0;
            $loan_application->save();
            session()->flash("success", "Loan applied successfully");
        } catch (\Throwable $th) {
            session()->flash("error", $th->getMessage());
        }
        return redirect()->route('loan-applications.index');
    }

    public function edit(Request $request, LoanProduct $loanProduct)
    {
        $user = Auth::user();
        if ($user->is_admin) {
            $partners = Partner::all();
        } else {
            $partners = Partner::where('id', $user->partner_id)->get();
        }
        $loan_product_types = LoanProductType::all();
        $loanProduct->load('loan_product_type', 'loan_product_terms');
        $payableAccounts = Account::where('partner_id', $user->partner_id)
            ->where('identifier', 'like', AccountSeederService::PAYABLES_IDENTIFIER . '.%')
            // ->where('position', '>', 2)
            ->get();

        return view('loan-applications.edit', compact('loan_product_types', 'partners', 'payableAccounts'));
    }

    public function update(Request $request, LoanProduct $loanProduct)
    {

        $loanProduct->update($request->all());
        $loanProduct->Whitelisted_Customers = json_encode($request->Whitelisted_Customers);
        $loanProduct->save();

        session()->flash("success", "Loan application updated successfully");
        return redirect()->route('loan-products.index');
    }

    public function show(LoanApplication $application)
    {
        $customerFiles = Storage::allFiles('customer/loan-applications/' . $application->id);
        $ussdHit = UssdHit::where('phone', $application->customer->Telephone_Number)->latest()->first();

        return view('loan-applications.show', compact('application', 'customerFiles', 'ussdHit'));
    }

    public function download(Request $request, LoanApplication $application)
    {
        return Storage::download($request->get('file'));
    }

    public function cancel()
    {
        session()->flash("success", "Loan application cancelled");
        return redirect()->route('loan-applications.index');
    }

    public function reject(Request $request, LoanApplication $loanApplication)
    {

        try {

            $request->validate([
                "Rejection_Reason" => "required|string",
                "Rejection_Reference" => "required|string",
            ]);

            $loanApplication->update([
                'Credit_Application_Status' => 'Rejected',
                'Rejection_Reason' => $request->Rejection_Reason,
                'Rejection_Reference' => $request->Rejection_Reference,
                'Rejection_Date' => now(),
            ]);

            $loanApplication->setStatus('Rejected');

            event(new LoanApplicationRejected($loanApplication));

            session()->flash("success", "Loan application rejected");
        } catch (\Throwable $e) {
            return $e->getMessage();
        }

        return redirect()->route('loan-applications.index');
    }

    public function delete(LoanApplication $loanApplication)
    {
        try {
            // Todo: MA
            // Don't allow deletion of loan products that has loans on it.
            $loanApplication->delete();
            session()->flash("success", "Loan application deleted successfully");
        } catch (\Throwable $th) {
            session()->flash("error", "Loan application cannot be deleted because it has been approved or disbursed");
        }
        return back();
    }
}
