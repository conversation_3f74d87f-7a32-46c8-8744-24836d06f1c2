<?php

namespace App\Jobs;

use App\Models\Transaction;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use App\Services\PaymentServiceManager;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class TransactableGetThroughPhoneJ<PERSON> implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $amount;
    public $fee;
    public $reason;
    public $phone_number;
    public $txn_reference;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public Transaction $transaction, $reason)
    {
        $this->phone_number = $this->transaction->Telephone_Number;
        $this->amount = $this->transaction->Amount;
        $this->fee = $this->transaction->savings_product_fee ?  $this->transaction->savings_product_fee->value : 0;
        $this->reason = $reason;
        $this->txn_reference = $this->transaction->TXN_ID;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        /**
         * The entire model was passed to the job, so we have to refresh it so that we get the latest data on the model
         */
        $this->transaction->refresh();

        $diffInMinutes = (int) now()->diffInMinutes($this->transaction->created_at, true);

        // todo: Make this configurable
        if ($diffInMinutes > 10) {
            Log::error('Transaction ' . $this->transaction->TXN_ID . ' is too old to process. Created ' . $diffInMinutes . ' minutes ago.');

            return;
        }

        try {
            /**
             * Down Payment transaction must have an asset_provider_id,
             * Ensure this before this transaction reaches here.
             */
            $paymentService = (new PaymentServiceManager($this->transaction))->paymentService;

            $response = $paymentService->collect($this->phone_number, $this->amount + $this->fee, $this->txn_reference, $this->reason);

            $this->transaction->update([
                // 'Status' => $response['message'],
                'Provider_TXN_ID' => data_get($response, 'reference'),
            ]);
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            Log::error($th->getTraceAsString());
        }
    }
}
