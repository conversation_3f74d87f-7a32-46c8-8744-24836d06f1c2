<?php

namespace App\Livewire\Dashboard;

use App\Actions\Reports\GetLoanArrearsReportDetailsAction;
use App\Models\Accounts\Account;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\LoanDisbursement;
use App\Models\LoanPenalty;
use App\Models\Transactables\SavingsDeposit;
use App\Models\Transactables\SavingsWithdraw;
use App\Services\Account\AccountSeederService;
use Illuminate\Container\Attributes\Auth;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth as FacadesAuth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use Livewire\Component;

class DashboardStats extends Component
{
    public function render()
    {
        // $loan->arrears_rate = $loan->schedule_sum_principal_remaining > 0 ? round($loan->total_principal_arrears / $loan->schedule_sum_principal_remaining * 100, 2) : 0;
        $loanSummary = DB::table('loans as l')
            ->selectRaw('
                sum(ls.interest_remaining) + sum(ls.principal_remaining) as total_outstanding,
                sum(ls.principal) - sum(ls.principal_remaining) as principal_paid,
                sum(ls.principal_remaining) as outstanding_principal,
                sum(ls.interest_remaining) as outstanding_interest,
                sum(ls.interest) - sum(ls.interest_remaining) as interest_paid,
                IFNULL(sum(lp.Amount_To_Pay), 0) as penalties_total,
                IFNULL(sum(lp.Amount), 0) as penalties_paid,
                sum(ls.total_outstanding) + (IFNULL(sum(lp.Amount_To_Pay), 0) - IFNULL(sum(lp.Amount), 0)) as outstanding,
                SUM(CASE
                    WHEN l.maturity_date < ? THEN principal_remaining
                    ELSE 0
                END) as defaulted_loan_amount
            ', [
                now()->toDateString()
            ])
            ->join('loan_schedules as ls', 'l.id', '=', 'ls.loan_id')
            ->leftJoin('loan_penalties as lp', 'l.id', '=', 'lp.loan_id')
            ->whereNotIn('l.credit_account_status', [
                Loan::ACCOUNT_STATUS_WRITTEN_OFF,
                Loan::ACCOUNT_STATUS_FULLY_PAID_OFF,
            ])
            ->where('l.partner_id', auth()->user()->partner_id)
            ->first();

        $fullyPaidLoans = Loan::query()->select('Credit_Account_Status')
            ->where('Credit_Account_Status', Loan::ACCOUNT_STATUS_FULLY_PAID_OFF)
            ->toBase()
            ->count();

        $rejectedLoans = LoanApplication::query()
            ->select('Credit_Application_Status')
            ->where('Credit_Application_Status', 'Rejected')
            ->count();

        $total_float_balance = 0;

        $disbursement_ova = Account::where('partner_id', auth()->user()->partner_id)
            ->where('slug', AccountSeederService::DISBURSEMENT_OVA_SLUG)
            ->first();

        if ($disbursement_ova) {
            $total_float_balance = $disbursement_ova->balance;
        }

        $totalIncome = Account::query()->where('partner_id', auth()->user()->partner_id)
            ->where('type_letter', '=', 'I')
            ->sum('balance');

        $arrears = $this->getArrearsStatistics();
        $defaultLoans = $arrears->total_arrears_amount;

        $disbursedLoansCount = Loan::query()->select('id')->has('disbursement')->count();
        $defaultRate = ($disbursedLoansCount === 0 ? 0 : round($arrears->loans_in_arrears_count / $disbursedLoansCount * 100, 2)) . '%';
        // sum principal remaining when payment_due_date is before today
        // sum principal remaining when payment_due_date is today or after today.
        $repaymentsQuery = DB::table('loan_schedules as ls')
            ->selectRaw('
                sum(case when payment_due_date < ? then (ls.principal - ls.principal_remaining) else 0 end) / (sum(case when payment_due_date < ? then principal_remaining else 0 end) + sum(case when payment_due_date >= ? then principal_remaining else 0 end)) * 100 as repayment_rate
            ', [now()->toDateString(), now()->toDateString(), now()->toDateString()])
            ->join('loans as l', 'l.id', '=', 'ls.loan_id')
            ->when(auth()->user()->partner_id, function($query, $partner_id){
                $query->where('l.partner_id', $partner_id);
            })->first();

        return view('livewire.dashboard.statistics', [
            'totalFloat' => number_format($total_float_balance),
            'principalOutstanding' => $loanSummary->outstanding_principal,
            'interestOutstanding' => number_format($loanSummary->outstanding_interest),
            'outstanding' => number_format($loanSummary->total_outstanding),
            'penaltyOutstanding' => number_format($loanSummary->penalties_total - $loanSummary->penalties_paid),
            'fullyPaidLoans' => number_format($fullyPaidLoans),
            'rejectedLoans' => number_format($rejectedLoans),
            'totalIncome' => number_format($totalIncome),
            'defaultLoans' => $defaultLoans,
            'defaultRate' => $defaultRate,
            'repaymentRate' => round($repaymentsQuery->repayment_rate, 2),
        ]);
    }
    public function getArrearsStatistics()
    {
        return Loan::query()
            ->whereNotIn('Credit_Account_Status', [
                Loan::ACCOUNT_STATUS_WRITTEN_OFF,
                Loan::ACCOUNT_STATUS_FULLY_PAID_OFF
            ])
            ->whereRelation('schedule', function ($query) {
                $query->where('principal_remaining', '>', 0)
                    ->where('payment_due_date', '<', now()->toDateString());
            })
            ->selectRaw('
                COUNT(DISTINCT loans.id) as loans_in_arrears_count,
                SUM(
                    IFNULL((SELECT SUM(principal_remaining)
                    FROM loan_schedules
                    WHERE loan_schedules.loan_id = loans.id
                    AND loan_schedules.principal_remaining > 0), 0)
                ) as total_arrears_amount')
            ->first();
    }
}
