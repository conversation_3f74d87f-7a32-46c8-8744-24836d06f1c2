<?php

namespace App\Models;

use App\Traits\HasStatuses;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;

class CustomerAsset extends Model
{
    use HasStatuses;

    protected $fillable = [
        'Identification',
        'Customer_ID',
        'Loan_ID',
        'Loan_Application_ID',
        'Status',
        'Submitted_At',
        'Submitted_By',
        'Status_Changed_At',
        'Status_Changed_Reason',
        'Disabled_At',
        'Last_Disabled_At',
        'Disbursed_At',
        'Disbursed_By',
        'Latitude',
        'Longitude',
        'Reposessed_Date',
        'Reposessed_By',
        'Disposed_Date',
        'Disposed_By',
    ];

    protected function casts(): array
    {
        return [
            'Disabled_At' => 'datetime',
            'Disbursed_At' => 'datetime',
            'Reposessed_Date' => 'datetime',
        ];
    }

    public function customer(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Customer::class, 'Customer_ID');
    }

    public function loanApplication(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(LoanApplication::class, 'Loan_Application_ID');
    }

    public function loan(): \Illuminate\Database\Eloquent\Relations\BelongsTo
    {
        return $this->belongsTo(Loan::class, 'Loan_ID');
    }

    public function canBeDisbursed(): bool
    {
        return strtolower($this->Status) === 'submitted';
    }

    public function location(): Attribute
    {
        return new Attribute(
            get: fn() => $this->Longitude . ', ' . $this->Latitude,
        );
    }
}
