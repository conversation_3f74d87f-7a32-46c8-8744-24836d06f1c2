<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{

  /**
   * Seed the application's database.
   */
  public function run(): void
  {
    $this->call(UserTableSeeder::class);
    $this->call(PartnerTableSeeder::class);
    $this->call(RoleSeeder::class);
    $this->call(PermissionSeeder::class);
    $this->call(RolePermissionSeeder::class);
    // $this->call(OVAAccountSeeder::class);
    // $this->call(BankAccountSeeder::class);
    // $this->call(FloatTopUpSeeder::class);
    // $this->call(TaxSeeder::class);
    // $this->call(CustomerSeeder::class);
    $this->call(LoanProductTypeSeeder::class);
    // $this->call(LoanProductFeeSeeder::class);
    // $this->call(LoanProductPenaltiesSeeder::class);
    // $this->call(LoanProductSeeder::class);
    // $this->call(SavingsProductSeeder::class);
    // $this->call(SavingsProductFeeSeeder::class);
    // $this->call(SavingsAccountSeeder::class);
    // $this->call(TransactionSeeder::class);
    // $this->call(TransactableSeeder::class);
    //$this->call(LoanApplicationSeeder::class);
    // $this->call(LoanSeeder::class);
    // $this->call(SmsLogSeeder::class);
    // $this->call(SmsTemplateSeeder::class);
    $this->call(DecisionEngineSeeder::class);
    $this->call(SwitchesTableSeeder::class);
  }
}
