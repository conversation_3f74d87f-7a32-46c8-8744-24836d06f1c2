<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    const customer_care_permission_incepts = [
        "view dashboard",
        "view loans",
        "view loan-applications",
        "view loan-accounts",
        "update users",
    ];

    const partner_admin_permission_excepts = [

        'view sms-float-top-up',
        'create sms-float-top-up',
        'delete sms-float-top-up',
        'update sms-float-top-up',

        'view exclusion-parameters',
        'create exclusion-parameters',
        'update exclusion-parameters',
        'delete exclusion-parameters',

        'view switches',
        'create switches',
        'update switches',
        'delete switches',
        'view partners',
        'create partners',
        'delete partners',

        'update tickets',
        'delete tickets',

        'view labels',

        'view categories',
        'view agents',
        'create agents',
        'update agents',
        'delete agents',
    ];

    const asset_provider_incepts = ['view dashboard', 'view downpayments', 'update downpayments', "update users"];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::table('role_has_permissions')->truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $admin_role = Role::findByName('Super Admin');
        // $customer_care_role = Role::findByName('Customer Care');
        // $asset_provider_role = Role::findByName('Asset Provider');

        foreach (Permission::all() as $permission) {
            $admin_role->givePermissionTo($permission);
            // Assign all permissions to partner admin role except the excepts
            if (!in_array($permission->name, self::partner_admin_permission_excepts)) {
                $partner_admin_roles = Role::where('name', 'Partner Admin')->get();
                foreach ($partner_admin_roles as $role) {
                    $role->givePermissionTo($permission);
                }
            }

            // Assign all permissions to customer care role only the incepts
            // if (in_array($permission->name, self::customer_care_permission_incepts)) {
            //     $customer_care_role->givePermissionTo($permission);
            // }

            // Assign all permissions to customer care role only the incepts
            // if (in_array($permission->name, self::asset_provider_incepts)) {
            //     $asset_provider_role->givePermissionTo($permission);
            // }
        }
    }
    // public static function getPartnerAdminRoles()
    // {
    //     $permissions = [];
    //     foreach (Permission::all() as $permission) {
    //         // Assign all permissions to partner admin role except the excepts
    //         if (!in_array($permission->name, self::partner_admin_permission_excepts)) {
    //             $permissions[] = $permission->name;
    //         }
    //     }

    //     return $permissions;
    // }
}
