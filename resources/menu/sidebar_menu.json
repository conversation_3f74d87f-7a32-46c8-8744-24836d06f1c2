{"menu": [{"name": "Dashboard", "icon": "menu-icon tf-icons bx bx-xs bx-home-smile", "url": "/", "slug": "dashboard"}, {"name": "Customers", "icon": "menu-icon tf-icons bx bx-xs bx-user", "url": "/customers", "slug": "customers"}, {"name": "SMS", "icon": "menu-icon tf-icons bx bx-xs bx-message-square-dots", "slug": "sms", "submenu": [{"name": "SMS Templates", "slug": "sms-templates", "url": "sms-templates"}, {"name": "Campaigns", "slug": "sms-campaigns", "url": "sms-campaigns"}, {"name": "SMS Logs", "slug": "sms-logs", "url": "sms-logs"}]}, {"name": "Switches", "icon": "menu-icon tf-icons bx bx-xs bx-user", "url": "/switches", "slug": "switches"}, {"icon": "menu-icon tf-icons bx bx-xs bx-user", "name": "Down Payments", "slug": "downpayments", "url": "/downpayments"}, {"icon": "menu-icon tf-icons bx bx-xs bx-message-square-dots", "name": "Asset Locations", "slug": "asset-locations", "url": "/asset-locations"}, {"icon": "menu-icon tf-icons bx bx-xs bx-message-square-dots", "name": "Cash Sales", "slug": "cash-sales", "url": "/cash-sales"}, {"name": "Partners", "icon": "menu-icon tf-icons bx bx-xs bx-building-house", "url": "/partners", "slug": "partners"}, {"name": "Decision Engine", "icon": "menu-icon tf-icons bx bx-xs bx bx-cog", "slug": "decision-engine", "submenu": [{"url": "/decision-engine/exclusion-parameters", "name": "Exclusion Parameters", "slug": "exclusion-parameters"}, {"url": "business-rules", "name": "Business Rules", "slug": "business-rules"}]}, {"name": "Loans", "icon": "menu-icon tf-icons bx bx-xs bx-money", "slug": "loans", "submenu": [{"name": "Loan Applications", "url": "/loan-applications", "slug": "loan-applications"}, {"name": "Loans", "url": "/loan-accounts", "slug": "loan-accounts"}, {"name": "Loan Products", "slug": "loan-products", "url": "/loan-products"}, {"name": "<PERSON><PERSON>", "url": "/loan-assets", "slug": "loan-accounts"}]}, {"name": "Savings", "icon": "menu-icon tf-icons bx bx-xs bx-money", "slug": "savings", "submenu": [{"name": "Savings Products", "slug": "savings-product", "url": "savings-product"}, {"name": "Savings Accounts", "url": "savings-accounts", "slug": "savings-accounts"}, {"name": "Deposits", "url": "savings-deposits", "slug": "savings-deposits"}, {"name": "Savings Preferences", "url": "savings-preferences", "slug": "savings-preferences"}, {"name": "Savings Providers", "url": "savings-providers", "slug": "savings-providers"}, {"name": "Savings Applications", "url": "savings-applications", "slug": "savings-applications"}, {"name": "Withdraws", "url": "savings-withdraws", "slug": "savings-withdraws"}]}, {"name": "Float Management", "icon": "menu-icon tf-icons bx bx-xs bx-detail", "slug": "float-management", "url": "float-management"}, {"name": "Loan Reports", "icon": "menu-icon tf-icons bx bx-xs bx bx-chart", "slug": "loan-report", "submenu": [{"url": "reports/loan-applications", "name": "Loan Applications", "slug": "loan-applications-report"}, {"url": "reports/loans/rejected/applications", "name": "Rejected Applications Report", "slug": "rejected-loans-report"}, {"url": "reports/loans/disbursement", "name": "Disbursement Report", "slug": "disbursement-report"}, {"url": "reports/loans/pending/disbursement", "name": "Pending Disbursements", "slug": "pending-disbursement-report"}, {"url": "/due-loans-report", "name": "Due Report", "slug": "due-loans-report"}, {"url": "reports/loans/repayment", "name": "Repayment Report", "slug": "repayment-report"}, {"url": "reports/loans/overdue", "name": "Arrears Report", "slug": "overdue-report"}, {"url": "reports/loans/paidoff", "name": "Paid Off Loans", "slug": "paidoff-report"}, {"url": "reports/loans/outstanding", "name": "Outstanding Loans", "slug": "outstanding-report"}, {"url": "reports/loans/interest-receivable", "name": "Interest Receivable", "slug": "loan-report"}, {"url": "reports/loans/penalties-receivable", "name": "Penalties Receivable", "slug": "loan-report"}, {"url": "reports/loans/consolidated", "name": "Consolidated Loans", "slug": "outstanding-report"}, {"url": "reports/loans/arrears-ageing-report", "name": "Ageing Report", "slug": "arrears-aging-report"}, {"url": "reports/loans/portfolio-at-risk", "name": "Portfolio At Risk Report", "slug": "portfolio-at-risk-report"}, {"url": "reports/customers/blacklisted", "name": "Black Listed Report", "slug": "blacklisted-report"}, {"url": "reports/loans/loss-provisions", "name": "Provision Report", "slug": "written-report"}, {"url": "reports/loans/written-off", "name": "Written-Off Report", "slug": "written-report"}, {"url": "reports/loans/written-off-recovered", "name": "Written-Off Recovered", "slug": "written-off-recovered-report"}]}, {"name": "Savings Reports", "icon": "menu-icon tf-icons bx bx-xs bx bx-line-chart", "slug": "savings-report", "submenu": [{"url": "reports/savings/summary", "name": "Saving Summary", "slug": "savings-summary-report"}, {"url": "reports/savings/deposits", "name": "Deposits Report", "slug": "savings-summary-report"}, {"url": "reports/savings/full-payments", "name": "Full Payment Report", "slug": "savings-summary-report"}, {"url": "reports/savings/withdrawals", "name": "Withdrawals Report", "slug": "savings-summary-report"}]}, {"name": "Financial Reports", "icon": "menu-icon tf-icons bx bx-xs bx bx-bar-chart-alt", "slug": "financial-reports", "submenu": [{"name": "Chart Of Accounts", "slug": "chart-of-accounts", "url": "chart-of-accounts"}, {"url": "reports/financial/trial-balance", "name": "Trial balance", "slug": "trial-balance-report"}, {"url": "reports/financial/balance-sheet", "name": "Balance sheet", "slug": "balance-sheet-report"}, {"url": "reports/financial/income-statement", "name": "Income statement", "slug": "income-statement-report"}, {"url": "reports/financial/cash-flow-statement", "name": "Cash flow statement", "slug": "cash-flow-statement"}]}, {"name": "Other Reports", "icon": "menu-icon tf-icons bx bx-xs bx bx-scatter-chart", "slug": "other-reports", "submenu": [{"name": "Borrowers Report", "slug": "borrowers-report", "url": "reports/others/borrowers-report", "partner_type": "loans"}, {"name": "Daily Report", "slug": "daily-report", "url": "reports/others/daily-report"}, {"name": "Transactions", "slug": "transactions", "url": "transactions"}, {"name": "Performance Metrics", "slug": "at-a-glance-report", "url": "reports/others/performance-metrics"}, {"url": "reports/financial/general-ledger-summary", "name": "General <PERSON><PERSON>", "slug": "general-ledger-summary"}, {"url": "reports/financial/income-report", "name": "Income Report", "slug": "income-report"}, {"url": "reports/financial/general-ledger-statement-break-down", "name": "GL Statement Break Down", "slug": "general-ledger-summary"}, {"name": "Scoring Hits", "url": "/hits", "slug": "hits"}, {"name": "SMS Report", "slug": "sms-logs", "url": "reports/sms-report"}, {"name": "Payment History Velocity", "slug": "payment-history-velocity-report", "url": "reports/others/payment-history-velocity"}]}, {"name": "User Control", "icon": "menu-icon tf-icons bx bx-xs bx-user", "slug": "users", "submenu": [{"name": "Users", "url": "/users", "slug": "users"}, {"name": "Roles", "url": "/roles", "slug": "roles"}]}, {"name": "Audit Trail", "icon": "menu-icon tf-icons bx bx-xs bx-book-content", "url": "audit-trail", "slug": "audit-trail"}, {"name": "Tickets", "icon": "menu-icon tf-icons bx bx-xs bx-support", "slug": "tickets", "submenu": [{"name": "Dashboard", "url": "/ticket-dashboard", "slug": "ticket-dashboard"}, {"name": "Tickets", "url": "/tickets", "slug": "tickets"}, {"name": "Reports", "url": "/ticket-reports", "slug": "ticket-reports"}, {"name": "Categories", "url": "/categories", "slug": "categories"}, {"name": "Labels", "url": "/labels", "slug": "labels"}, {"name": "Agents", "url": "/agents", "slug": "agents"}]}]}