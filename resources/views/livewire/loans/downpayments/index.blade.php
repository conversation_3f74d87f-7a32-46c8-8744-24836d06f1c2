<div class="card">
    <div class="card-body">
        {{-- <h5 class="mb-0">Loan Application</h5> --}}
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="input-group">
                    <input type="text" wire:model.live="searchTerm" class="form-control"
                        placeholder="Type to search..." aria-label="Search" />
                </div>
            </div>
            <div class="col-md-8 text-end">
            
            </div>
        </div>
        
        <div class="table-responsive text-nowrap">
            <table class="table">
                <thead>
                    <tr>
                        <th>Loan Application ID</th>
                        <th>Customer Name</th>
                        <th class="text-end">Telephone Number</th>
                        <th class="text-end">Amount</th>
                        <th class="text-end">Status</th>
                        <th class="text-end">Payment Date</th>
                        <th class="text-end">Duration (Days)</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody class="table-border-bottom-0">
                    @foreach ($records as $loanApplication)
                        <tr>
                            <td>{{ $loanApplication->application_number }}</td>
                            <td>{{ $loanApplication->customer->full_name }}</td>
                            <td class="text-end">{{ $loanApplication->customer->Telephone_Number }}</td>
                            <td class="text-end"><x-money :value="$loanApplication->downPayment->Amount" /></td>
                            <td class="text-end">{{ $loanApplication->downPayment->Asset_Disbursement_Status }}</td>
                            <td class="text-end">{{ $loanApplication->downPayment->created_at }}</td>
                            <td class="text-end">{{ $loanApplication->downPayment->duration }}</td>
                            <td class="text-end">
                                @if($loanApplication->downPayment->canBeRefunded())
                                  <div class="btn-group">
                                    <a href="{{ route('downpayments.show', $loanApplication) }}"
                                       class="btn btn-sm btn-outline-danger">Refund</a>
                                  </div>
                                @endif
                                  <div class="btn-group">
                                      <a href="{{ route('downpayments.show', $loanApplication) }}"
                                          class="btn btn-sm btn-outline-info">View Details</a>
                                  </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
            <div class="pagination">
                {{ $records->links() }}
            </div>
        </div>
    </div>
    
</div>
