@extends('layouts/contentNavbarLayout')
@section('title', 'Loan Product Penalty - Create')
@section('content')
<div class="row">
    <div class="col mb-6 order-0">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Edit Loan Product Penalty : {{$penalty->Name}}</h5>
            </div>
            <div class="d-flex align-items-start row">
                <div class="col-sm-7">
                    <div class="card-body">
                        <div>
                            <form action="{{ route('loan-product-penalty.update', $penalty)}}" method='post'>
                                @csrf
                                @method('PUT')
                                <div class="mb-4">
                                    <label for="Partner_ID" class="form-label">Partner Name</label>
                                    <select class="form-select" id="type" name="Partner_ID" required>
                                        <option value="">Choose...</option>
                                        <?php foreach (App\Models\Partner::all() as $partner): ?>
                                            <option value="{{$partner->id}}" {{ $partner->id == $penalty->Partner_ID ? 'selected' : '' }}>{{$partner->Institution_Name}}</option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label for="Loan_Product_ID" class="form-label">Product Name</label>
                                    <select class="form-select" id="type" name="Loan_Product_ID" required>
                                        <option value="">Choose...</option>
                                        <?php foreach (App\Models\LoanProduct::all() as $product): ?>
                                            <option value="{{$product->id}}" {{ $product->id == $penalty->Loan_Product_ID ? 'selected' : '' }}>{{$product->Name}}</option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label for="Name" class="form-label">Name</label>
                                    <input type="text" class="form-control" id="Name" name="Name" value="{{$penalty->Name}}" required />
                                </div>
                                <div class="mb-4">
                                    <label for="Calculation_Method" class="form-label">Calculation Method</label>
                                    <select class="form-select" id="type" name="Calculation_Method" required>
                                        <option value="">Choose...</option>
                                        <option value="Percentage" {{ $penalty->Calculation_Method == "Percentage" ? 'selected' : '' }}>Percentage</option>
                                        <option value="Amount" {{ $penalty->Calculation_Method == "Amount" ? 'selected' : '' }}>Amount</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label for="Value" class="form-label">Value</label>
                                    <input type="number" class="form-control" id="Value" name="Value" value="{{$penalty->Value}}" required />
                                </div>
                                <div class="mb-4">
                                    <label for="Applicable_On" class="form-label">Applicable On</label>
                                    <select class="form-select" id="type" name="Applicable_On" required>
                                        <option selected>Choose...</option>
                                        <option value="Balance" {{ $penalty->Applicable_On == "Balance" ? 'selected' : '' }}>Balance</option>
                                        <option value="Principal" {{ $penalty->Applicable_On == "Principal" ? 'selected' : '' }}>Principal</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label for="Description" class="form-label">Description</label>
                                    <textarea name="Description" id="Description" cols="30" rows="3" class="form-control" required> {{$penalty->Description}}"</textarea>
                                    <div id="Description" class="form-text">Please describe in detail what
                                        this Fee is and what it's for in a sentence.</div>
                                </div>
                                <div class="mb-4">
                                    <label for="Has_Recurring_Penalty" class="form-label">Has Recurring Penalty</label>
                                    <select class="form-select" id="type" name="Has_Recurring_Penalty" required>
                                        <option value="">Choose...</option>
                                        <option value="0" {{ $penalty->Has_Recurring_Penalty == "0" ? 'selected' : '' }}>No</option>
                                        <option value="1" {{ $penalty->Has_Recurring_Penalty == "1" ? 'selected' : '' }}>Yes</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label for="Recurring_Penalty_Interest_Period_Type" class="form-label">Recurring Penalty Interest Period Type</label>
                                    <select class="form-select" id="type" name="Recurring_Penalty_Interest_Period_Type">
                                        <option value="">Choose...</option>
                                        <option value="Days" {{ $penalty->Recurring_Penalty_Interest_Period_Type == "Days" ? 'selected' : '' }}>Days</option>
                                        <option value="Weeks" {{ $penalty->Recurring_Penalty_Interest_Period_Type == "Weeks" ? 'selected' : '' }}>Weeks</option>
                                        <option value="Months" {{ $penalty->Recurring_Penalty_Interest_Period_Type == "Months" ? 'selected' : '' }}>Months</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label for="Recurring_Penalty_Interest_Period_Value" class="form-label">Recurring Penalty Interest Period Value</label>
                                    <input type="number" class="form-control" id="Recurring_Penalty_Interest_Period_Value" name="Recurring_Penalty_Interest_Period_Value" value="{{$penalty->Recurring_Penalty_Interest_Period_Value}}" />
                                </div>
                                <div class="mb-4">
                                    <label for="Recurring_Penalty_Interest_Value" class="form-label">Recurring Penalty Interest Value</label>
                                    <input type="number" class="form-control" id="Recurring_Penalty_Interest_Value" name="Recurring_Penalty_Interest_Value" value="{{$penalty->Recurring_Penalty_Interest_Value}}" />
                                </div>

                                <button type="submit" class="btn btn-dark">Submit</button>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
@endsection
