@extends('pdf.layouts')

@section('content')
    <div class="text-center">
        <h2 style="margin-bottom: 5px; margin-top: 0; font-size: 16px">{{ $partner->Institution_Name }}</h2>
        <h4 style="margin-top: 0; margin-bottom: 4px">General Ledger Summary Report</h4>
        <p style="margin-top: 0; font-size: 10px">From {{ $filters['startDate'] }} to {{ $filters['endDate'] }}</p>
    </div>

    <table class="table table-bordered">
        <thead>
        <tr class="table-header">
            <th class="text-start">Account Name</th>
            <th class="text-end">Total Debit</th>
            <th class="text-end">Total Credit</th>
            <th class="text-end">Balance</th>
        </tr>
        </thead>
        <tbody>
        @foreach ($records as $entry)
            <tr>
                <td>{{ $entry->name }}</td>
                <td class="text-end"><x-money :value="$entry->total_debit" /></td>
                <td class="text-end"><x-money :value="$entry->total_credit" /></td>
                <td class="text-end"><x-money :value="$entry->balance" /></td>
            </tr>
        @endforeach
        </tbody>
    </table>
    <x-print-footer/>
@endsection
