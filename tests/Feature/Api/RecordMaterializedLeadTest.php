<?php

namespace Tests\Feature\Api;

use App\Models\Customer;
use App\Models\Partner;
use App\Models\PartnerApiSetting;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class RecordMaterializedLeadTest extends TestCase
{
    use DatabaseTransactions, WithFaker;

    public function test_it_can_record_a_materialized_lead()
    {
        // Create a partner
        $partner = Partner::factory()->create();

        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        // Create a customer
        $customer = Customer::factory()->create();

        $payload = [
            'customer_id' => $customer->id,
            'mobile_number' => '256789123456',
            'asset_financier' => $this->faker->company,
            'vehicle_reg_number' => $this->faker->regexify('[A-Z]{3}[0-9]{3}[A-Z]'),
            'vehicle_delivery_date' => $this->faker->date('Y-m-d', 'now'),
        ];

        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/leads/materialized', $payload);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'customer_id',
                    'mobile_number',
                    'asset_financier',
                    'vehicle_reg_number',
                    'vehicle_delivery_date',
                    'created_at',
                    'updated_at',
                ]
            ]);

        $this->assertDatabaseHas('materialized_leads', [
            'customer_id' => $payload['customer_id'],
            'telephone_number' => $payload['mobile_number'],
            'asset_financier' => $payload['asset_financier'],
            'vehicle_registration_number' => $payload['vehicle_reg_number'],
            'partner_id' => $partner->id,
        ]);
    }

    public function test_it_validates_required_fields()
    {
        // Create a partner
        $partner = Partner::factory()->create();
        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/leads/materialized', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['customer_id', 'mobile_number', 'asset_financier', 'vehicle_reg_number', 'vehicle_delivery_date']);
    }

    public function test_it_validates_vehicle_delivery_date_is_a_valid_date()
    {
        // Create a partner
        $partner = Partner::factory()->create();

        $api_key = $this->faker->text(10);

        $partner_api_setting = new PartnerApiSetting();
        $partner_api_setting->partner_id = $partner->id;
        $partner_api_setting->api_key = $api_key;
        $partner_api_setting->expires_at = now()->addMinutes(5);
        $partner_api_setting->save();

        // Create a customer
        $customer = Customer::factory()->create();

        $payload = [
            'customer_id' => $customer->id,
            'mobile_number' => $this->faker->phoneNumber,
            'asset_financier' => $this->faker->company,
            'vehicle_reg_number' => $this->faker->regexify('[A-Z]{3}[0-9]{3}[A-Z]'),
            'vehicle_delivery_date' => 'not-a-date',
        ];

        $response = $this->withHeader('X-PARTNER-CODE', $partner->Identification_Code)
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/leads/materialized', $payload);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['vehicle_delivery_date']);
    }

    public function test_it_requires_partner_code_header()
    {
        $api_key = $this->faker->text(10);

        $payload = [
            'customer_id' => $this->faker->uuid,
            'mobile_number' => $this->faker->phoneNumber,
            'asset_financier' => $this->faker->company,
            'vehicle_reg_number' => $this->faker->regexify('[A-Z]{3}[0-9]{3}[A-Z]'),
            'vehicle_delivery_date' => $this->faker->date,
        ];

        $response = $this->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/leads/materialized', $payload);

        $response->assertStatus(401)
            ->assertJson([
                'message' => 'Unauthorized. Your secure partner code is required.',
            ]);
    }

    public function test_it_validates_partner_code_is_valid()
    {
        $api_key = $this->faker->text(10);

        $payload = [
            'customer_id' => $this->faker->uuid,
            'mobile_number' => $this->faker->phoneNumber,
            'asset_financier' => $this->faker->company,
            'vehicle_reg_number' => $this->faker->regexify('[A-Z]{3}[0-9]{3}[A-Z]'),
            'vehicle_delivery_date' => $this->faker->date,
        ];

        $response = $this->withHeader('X-PARTNER-CODE', 'invalid-code')
            ->withHeader('Authorization', "Bearer $api_key")
            ->postJson('/api/leads/materialized', $payload);

        $response->assertStatus(401)
            ->assertJson([
                'message' => 'Unauthorized. Invalid partner code.',
            ]);
    }
}
