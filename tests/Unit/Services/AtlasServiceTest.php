<?php

namespace Tests\Unit\Services;

use App\Services\Integrations\AtlasService;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AtlasServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        // Configure the test environment
        config([
            'services.partners.spiro_atlas.url' => 'https://test-api.example.com',
            'services.partners.spiro_atlas.client_key' => 'test-client-key',
            'services.partners.spiro_atlas.client_secret' => 'test-client-secret',
            'services.partners.spiro_atlas.refresh_token' => 'test-refresh-token',
            'services.partners.spiro_atlas.source_id' => 'test-source-id',
        ]);
    }

    public function test_it_uploads_file_correctly()
    {
        // Create a test file
        Storage::fake('local');
        $testDate = now()->format('Ymd');
        $testFilePath = "partners/spiro/uploads/{$testDate}/test-file.csv";
        $testFileContent = "id,name,email\n1,<PERSON>,<EMAIL>";
        Storage::disk('local')->put($testFilePath, $testFileContent);

        // Mock HTTP responses
        Http::fake([
            // Mock the token endpoint
            'https://test-api.example.com/oauth/token*' => Http::response([
                'access_token' => 'test-access-token',
                'expires_in' => 3600,
            ]),
            // Mock the upload endpoint
            'https://test-api.example.com/lead/upload' => Http::response([
                'status' => 'success',
            ]),
        ]);

        // Create the service and upload the file
        $service = new AtlasService();
        $result = $service->upload($testDate);

        // Assert the result is true (successful upload)
        $this->assertTrue($result);

        // Assert the file was moved to the backup directory
        $this->assertFalse(Storage::disk('local')->exists($testFilePath));
        $this->assertTrue(Storage::disk('local')->exists('partners/spiro/backup/test-file.csv'));

        // Assert the HTTP request was made correctly
        Http::assertSent(function (Request $request) {
            return $request->url() === 'https://test-api.example.com/lead/upload' &&
                   $request->hasHeader('bearer_token', 'test-access-token') &&
                   $request->hasHeader('source-id', 'test-source-id') &&
                   $request->hasFile('file');
        });
    }

    public function test_it_returns_false_when_no_files_to_upload()
    {
        // Create a fake storage with no files
        Storage::fake('local');

        // Mock HTTP responses for token
        Http::fake([
            'https://test-api.example.com/oauth/token*' => Http::response([
                'access_token' => 'test-access-token',
                'expires_in' => 3600,
            ]),
        ]);

        // Create the service and try to upload
        $service = new AtlasService();
        $result = $service->upload();

        // Assert the result is false (no files to upload)
        $this->assertFalse($result);

        // Assert no HTTP request was made to the upload endpoint
        Http::assertNotSent(function (Request $request) {
            return $request->url() === 'https://test-api.example.com/lead/upload';
        });
    }
}
