<?php

namespace Tests\Unit\Services;

use App\Exceptions\MtnApiException;
use App\Services\MockMtnApiService;
use App\Services\MtnApiService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class MtnApiServiceTest extends TestCase
{
    private MtnApiService $service;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up complete test configuration
        Config::set('services.mtn.test', [
            'url' => 'https://test.mtn.api',
            'base_context' => 'test',
            'verify_ssl' => false,
            'timeout' => 5,
            'debug' => true,
            'savings_username' => 'test_savings_user',
            'savings_password' => 'test_savings_pass',
            'loans_username' => 'test_loans_user',
            'loans_password' => 'test_loans_pass',
            'dfcu_ca_certificate' => 'certificates/test_ca.pem',
            'dfcu_certificate' => 'certificates/test_cert.pem',
            'lms_private_key' => 'certificates/test_key.pem',
            'm3_external_certificate' => 'certificates/test_m3.pem',
        ]);

        Config::set('services.mtn.production', [
            'url' => 'https://prod.mtn.api',
            'base_context' => 'sp',
            'verify_ssl' => true,
            'timeout' => 30,
            'debug' => false,
            'savings_username' => 'prod_savings_user',
            'savings_password' => 'prod_savings_pass',
            'loans_username' => 'prod_loans_user',
            'loans_password' => 'prod_loans_pass',
            'dfcu_ca_certificate' => 'certificates/prod_ca.pem',
            'dfcu_certificate' => 'certificates/prod_cert.pem',
            'lms_private_key' => 'certificates/prod_key.pem',
            'm3_external_certificate' => 'certificates/prod_m3.pem',
        ]);

        $this->service = new MtnApiService('test');
    }


    public function test_loads_test_environment_configuration_correctly()
    {
        $service = new MtnApiService('test');

        $this->assertEquals('https://test.mtn.api', $service->getBaseUrl());
        $this->assertEquals('test_loans_user', $service->getLoansUsername());
        $this->assertEquals('test_savings_user', $service->getSavingsUsername());
    }

    public function test_loads_production_environment_configuration_correctly(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $service = new MtnApiService('production');

        $this->assertEquals('https://prod.mtn.api', $service->getBaseUrl());
        $this->assertEquals('prod_loans_user', $service->getLoansUsername());
        $this->assertEquals('prod_savings_user', $service->getSavingsUsername());
    }


    public function test_uses_default_values_when_config_is_missing(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        // Create a new environment config that doesn't exist to test defaults
        $service = new MtnApiService('missing_env');

        // Should use defaults when config values are missing
        $this->assertEquals('sp', $service->getBaseContext()); // Default value
        $this->assertEquals(30, $service->getTimeout()); // Default value
        $this->assertTrue($service->getVerifySsl()); // Default value
        $this->assertFalse($service->getDebug()); // Default value
    }


    public function test_handles_case_insensitive_environment_names(): void
    {
        $service1 = new MtnApiService('TEST');
        $service2 = new MtnApiService('Test');
        $service3 = new MtnApiService('test');

        $this->assertEquals($service1->getBaseUrl(), $service2->getBaseUrl());
        $this->assertEquals($service2->getBaseUrl(), $service3->getBaseUrl());
    }


    public function test_can_override_config_with_constructor_array(): void
    {
        $customConfig = ['mtn_url' => 'https://custom.mtn.api'];
        $service = new MtnApiService('test', $customConfig);

        $this->assertEquals('https://custom.mtn.api', $service->getBaseUrl());
    }


    public function test_initializes_all_required_properties(): void
    {
        $service = new MtnApiService('test');

        // Test that all properties are accessible via getters
        $this->assertNotEmpty($service->getBaseUrl());
        $this->assertNotEmpty($service->getBaseContext());
        $this->assertNotEmpty($service->getLoansUsername());
        $this->assertNotEmpty($service->getSavingsUsername());
        $this->assertIsInt($service->getTimeout());
        $this->assertIsBool($service->getVerifySsl());
        $this->assertIsBool($service->getDebug());
    }

    public function test_loads_certificate_paths_correctly(): void
    {
        $service = new MtnApiService('test');

        $this->assertEquals('certificates/test_ca.pem', $service->getDfcuCaCertificate());
        $this->assertEquals('certificates/test_cert.pem', $service->getDfcuCertificate());
        $this->assertEquals('certificates/test_key.pem', $service->getLmsPrivateKey());
        $this->assertEquals('certificates/test_m3.pem', $service->getM3ExternalCertificate());
    }

    public function test_loads_authentication_credentials_correctly(): void
    {
        $service = new MtnApiService('test');

        $this->assertEquals('test_loans_user', $service->getLoansUsername());
        $this->assertEquals('test_loans_pass', $service->getLoansPassword());
        $this->assertEquals('test_savings_user', $service->getSavingsUsername());
        $this->assertEquals('test_savings_pass', $service->getSavingsPassword());
    }


    public function test_handles_missing_environment_configuration_gracefully(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $service = new MtnApiService('nonexistent');

        // Should use defaults for missing config
        $this->assertIsString($service->getBaseUrl()); // Should be empty string
        $this->assertEquals('sp', $service->getBaseContext()); // Default value
        $this->assertEquals(30, $service->getTimeout()); // Default value
        $this->assertTrue($service->getVerifySsl()); // Default value
        $this->assertFalse($service->getDebug()); // Default value
    }


    public function test_validates_configuration_completeness_for_test_environment(): void
    {
        $service = new MtnApiService('test');

        // Verify all required configuration is loaded
        $this->assertNotEmpty($service->getBaseUrl(), 'Base URL should not be empty');
        $this->assertNotEmpty($service->getBaseContext(), 'Base context should not be empty');
        $this->assertNotEmpty($service->getLoansUsername(), 'Loans username should not be empty');
        $this->assertNotEmpty($service->getLoansPassword(), 'Loans password should not be empty');
        $this->assertNotEmpty($service->getSavingsUsername(), 'Savings username should not be empty');
        $this->assertNotEmpty($service->getSavingsPassword(), 'Savings password should not be empty');
        $this->assertNotEmpty($service->getDfcuCaCertificate(), 'DFCU CA certificate path should not be empty');
        $this->assertNotEmpty($service->getDfcuCertificate(), 'DFCU certificate path should not be empty');
        $this->assertNotEmpty($service->getLmsPrivateKey(), 'LMS private key path should not be empty');
        $this->assertNotEmpty($service->getM3ExternalCertificate(), 'M3 external certificate path should not be empty');
    }


    public function test_validates_configuration_completeness_for_production_environment(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $service = new MtnApiService('production');

        // Verify all required configuration is loaded
        $this->assertNotEmpty($service->getBaseUrl(), 'Base URL should not be empty');
        $this->assertNotEmpty($service->getBaseContext(), 'Base context should not be empty');
        $this->assertNotEmpty($service->getLoansUsername(), 'Loans username should not be empty');
        $this->assertNotEmpty($service->getLoansPassword(), 'Loans password should not be empty');
        $this->assertNotEmpty($service->getSavingsUsername(), 'Savings username should not be empty');
        $this->assertNotEmpty($service->getSavingsPassword(), 'Savings password should not be empty');
        $this->assertNotEmpty($service->getDfcuCaCertificate(), 'DFCU CA certificate path should not be empty');
        $this->assertNotEmpty($service->getDfcuCertificate(), 'DFCU certificate path should not be empty');
        $this->assertNotEmpty($service->getLmsPrivateKey(), 'LMS private key path should not be empty');
        $this->assertNotEmpty($service->getM3ExternalCertificate(), 'M3 external certificate path should not be empty');
    }


    public function test_has_different_configurations_for_test_and_production(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $testService = new MtnApiService('test');
        $prodService = new MtnApiService('production');

        // URLs should be different
        $this->assertNotEquals($testService->getBaseUrl(), $prodService->getBaseUrl());

        // SSL verification should be different (test: false, prod: true)
        $this->assertFalse($testService->getVerifySsl());
        $this->assertTrue($prodService->getVerifySsl());

        // Debug should be different (test: true, prod: false)
        $this->assertTrue($testService->getDebug());
        $this->assertFalse($prodService->getDebug());

        // Usernames should be different
        $this->assertNotEquals($testService->getLoansUsername(), $prodService->getLoansUsername());
        $this->assertNotEquals($testService->getSavingsUsername(), $prodService->getSavingsUsername());
    }


    public function test_validates_ssl_configuration_properly(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $testService = new MtnApiService('test');
        $prodService = new MtnApiService('production');

        // Test environment should have SSL verification disabled
        $this->assertFalse($testService->getVerifySsl());

        // Production environment should have SSL verification enabled
        $this->assertTrue($prodService->getVerifySsl());
    }


    public function test_validates_timeout_configuration(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $testService = new MtnApiService('test');
        $prodService = new MtnApiService('production');

        // Test environment should have shorter timeout
        $this->assertEquals(5, $testService->getTimeout());

        // Production environment should have longer timeout
        $this->assertEquals(30, $prodService->getTimeout());
    }


    public function test_validates_debug_configuration(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $testService = new MtnApiService('test');
        $prodService = new MtnApiService('production');

        // Test environment should have debug enabled
        $this->assertTrue($testService->getDebug());

        // Production environment should have debug disabled
        $this->assertFalse($prodService->getDebug());
    }


    public function test_creates_guzzle_client_with_correct_configuration(): void
    {
        $service = new MtnApiService('test');

        // Test loans client
        $loansClient = $service->getClient();
        $this->assertInstanceOf(\GuzzleHttp\Client::class, $loansClient);

        // Test savings client
        $savingsClient = $service->getClient('savings');
        $this->assertInstanceOf(\GuzzleHttp\Client::class, $savingsClient);
    }


    public function test_validates_certificate_paths_are_configured(): void
    {
        $service = new MtnApiService('test');

        // All certificate paths should be configured
        $this->assertStringContainsString('certificates/', $service->getDfcuCaCertificate());
        $this->assertStringContainsString('certificates/', $service->getDfcuCertificate());
        $this->assertStringContainsString('certificates/', $service->getLmsPrivateKey());
        $this->assertStringContainsString('certificates/', $service->getM3ExternalCertificate());

        // Certificate paths should end with .pem
        $this->assertStringEndsWith('.pem', $service->getDfcuCaCertificate());
        $this->assertStringEndsWith('.pem', $service->getDfcuCertificate());
        $this->assertStringEndsWith('.pem', $service->getLmsPrivateKey());
        $this->assertStringEndsWith('.pem', $service->getM3ExternalCertificate());
    }


    public function test_validates_authentication_credentials_format(): void
    {
        $service = new MtnApiService('test');

        // Usernames should not be empty and should be strings
        $this->assertIsString($service->getLoansUsername());
        $this->assertIsString($service->getSavingsUsername());
        $this->assertNotEmpty($service->getLoansUsername());
        $this->assertNotEmpty($service->getSavingsUsername());

        // Passwords should not be empty and should be strings
        $this->assertIsString($service->getLoansPassword());
        $this->assertIsString($service->getSavingsPassword());
        $this->assertNotEmpty($service->getLoansPassword());
        $this->assertNotEmpty($service->getSavingsPassword());
    }


    public function test_validates_base_url_format(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $testService = new MtnApiService('test');
        $prodService = new MtnApiService('production');

        // URLs should be valid HTTP/HTTPS URLs
        $this->assertStringStartsWith('https://', $testService->getBaseUrl());
        $this->assertStringStartsWith('https://', $prodService->getBaseUrl());

        // URLs should not end with slash
        $this->assertNotEquals('/', substr($testService->getBaseUrl(), -1));
        $this->assertNotEquals('/', substr($prodService->getBaseUrl(), -1));
    }


    public function test_validates_service_provider_name(): void
    {
        $service = new MtnApiService('test');

        $this->assertEquals('MTN', $service->getServiceProviderName());
    }


    public function test_validates_weekend_check_functionality(): void
    {
        $service = new MtnApiService('test');

        // In test environment, isWeekend should always return true
        $this->assertTrue($service->isWeekend());
    }


    public function test_validates_status_constants_are_defined(): void
    {
        $this->assertEquals('SUCCEEDED', MtnApiService::STATUS_SUCCESS);
        $this->assertEquals('PENDING', MtnApiService::STATUS_PENDING);
        $this->assertEquals('FAILED', MtnApiService::STATUS_FAILED);
    }

    public function test_validates_configuration_with_missing_optional_values(): void
    {
        // Set empty string values instead of null to avoid type errors
        Config::set('services.mtn.test.dfcu_ca_certificate', '');
        Config::set('services.mtn.test.m3_external_certificate', '');

        $service = new MtnApiService('test');

        // Should handle missing optional values gracefully
        $this->assertEquals('', $service->getDfcuCaCertificate());
        $this->assertEquals('', $service->getM3ExternalCertificate());

        // Required values should still be present
        $this->assertNotEmpty($service->getBaseUrl());
        $this->assertNotEmpty($service->getLoansUsername());
    }


    public function test_validates_environment_specific_certificate_paths(): void
    {
        // Mock the app environment to be local so isWeekend returns true
        $this->app->instance('env', 'local');

        $testService = new MtnApiService('test');
        $prodService = new MtnApiService('production');

        // Test environment certificates
        $this->assertStringContainsString('test_', $testService->getDfcuCaCertificate());
        $this->assertStringContainsString('test_', $testService->getDfcuCertificate());
        $this->assertStringContainsString('test_', $testService->getLmsPrivateKey());
        $this->assertStringContainsString('test_', $testService->getM3ExternalCertificate());

        // Production environment certificates
        $this->assertStringContainsString('prod_', $prodService->getDfcuCaCertificate());
        $this->assertStringContainsString('prod_', $prodService->getDfcuCertificate());
        $this->assertStringContainsString('prod_', $prodService->getLmsPrivateKey());
        $this->assertStringContainsString('prod_', $prodService->getM3ExternalCertificate());
    }


    public function test_validates_constructor_throws_exception_on_weekdays_in_production(): void
    {
        // This test would need to be adjusted based on actual weekend logic
        // For now, we test that the constructor completes successfully in test environment
        $this->assertInstanceOf(MtnApiService::class, new MtnApiService('test'));
    }


    public function test_validates_all_configuration_keys_are_loaded()
    {
        $service = new MtnApiService('test');

        // Test that all expected configuration keys are loaded and accessible
        $configKeys = [
            'baseUrl' => $service->getBaseUrl(),
            'baseContext' => $service->getBaseContext(),
            'verifySsl' => $service->getVerifySsl(),
            'timeout' => $service->getTimeout(),
            'debug' => $service->getDebug(),
            'dfcuCaCertificate' => $service->getDfcuCaCertificate(),
            'dfcuCertificate' => $service->getDfcuCertificate(),
            'lmsPrivateKey' => $service->getLmsPrivateKey(),
            'm3ExternalCertificate' => $service->getM3ExternalCertificate(),
            'loansUsername' => $service->getLoansUsername(),
            'loansPassword' => $service->getLoansPassword(),
            'savingsUsername' => $service->getSavingsUsername(),
            'savingsPassword' => $service->getSavingsPassword(),
        ];

        foreach ($configKeys as $key => $value) {
            $this->assertNotNull($value, "Configuration key '{$key}' should not be null");
        }
    }


    public function test_validates_configuration_data_types(): void
    {
        $service = new MtnApiService('test');

        // String properties
        $this->assertIsString($service->getBaseUrl());
        $this->assertIsString($service->getBaseContext());
        $this->assertIsString($service->getDfcuCaCertificate());
        $this->assertIsString($service->getDfcuCertificate());
        $this->assertIsString($service->getLmsPrivateKey());
        $this->assertIsString($service->getM3ExternalCertificate());
        $this->assertIsString($service->getLoansUsername());
        $this->assertIsString($service->getLoansPassword());
        $this->assertIsString($service->getSavingsUsername());
        $this->assertIsString($service->getSavingsPassword());

        // Boolean properties
        $this->assertIsBool($service->getVerifySsl());
        $this->assertIsBool($service->getDebug());

        // Integer properties
        $this->assertIsInt($service->getTimeout());
        $this->assertGreaterThan(0, $service->getTimeout());
    }
}
