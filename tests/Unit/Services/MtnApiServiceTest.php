<?php

namespace Tests\Unit\Services;

use App\Exceptions\MtnApiException;
use App\Services\MockMtnApiService;
use App\Services\MtnApiService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class MtnApiServiceTest extends TestCase
{
    private MockMtnApiService $service;

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('services.mtn', [
            'url' => 'https://test.mtn.api',
            'base_context' => 'test',
            'verify_ssl' => false,
            'timeout' => 5,
            'debug' => true
        ]);

        $this->service = new MtnApiService();
    }

    public function test_it_validates_phone_number_format()
    {
        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Invalid phone number format. Must be in format: 256XXXXXXXXX');

        $this->service->getLoanLimit('1234567890');
    }

    public function test_it_validates_amount_is_positive()
    {
        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Amount must be greater than 0');

        $this->service->disburse('**********89', 0, 'test-ref');
    }

    public function test_it_validates_tenor_range()
    {
        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Tenor must be between 1 and 30 days');

        $this->service->createLoanApplication('**********89', 1000, 31);
    }

    public function test_it_handles_account_holder_not_found_error()
    {
        Http::fake([
            'test/disbursement' => Http::response('<?xml version="1.0" encoding="UTF-8"?><error><errorcode>ACCOUNTHOLDER_NOT_FOUND</errorcode></error>', 500)
        ]);

        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Account holder not found in the system');

        $this->service->disburse('**********89', 1000, 'test-ref');
    }

    public function test_it_handles_insufficient_funds_error()
    {
        Http::fake([
            'test/disbursement' => Http::response('<?xml version="1.0" encoding="UTF-8"?><error><errorcode>INSUFFICIENT_FUNDS</errorcode></error>', 500)
        ]);

        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Insufficient funds for transaction');

        $this->service->disburse('**********89', 1000, 'test-ref');
    }

    public function test_it_validates_disbursement_response()
    {
        Http::fake([
            'test/disbursement' => Http::response('<?xml version="1.0" encoding="UTF-8"?><response></response>', 200)
        ]);

        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Invalid disbursement response: missing transaction ID');

        $this->service->disburse('**********89', 1000, 'test-ref');
    }

    public function test_it_validates_customer_details_response()
    {
        Http::fake([
            'test/getcustomerdetails' => Http::response('<?xml version="1.0" encoding="UTF-8"?><response></response>', 200)
        ]);

        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Invalid customer details response: missing status');

        $this->service->getCustomerDetails('**********89');
    }

    public function test_it_validates_loan_application_response()
    {
        Http::fake([
            'test/initiateloanapplication' => Http::response('<?xml version="1.0" encoding="UTF-8"?><response></response>', 200)
        ]);

        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Invalid loan application response: missing loan account details');

        $this->service->createLoanApplication('**********89', 1000, 7);
    }

    public function test_it_handles_successful_disbursement()
    {
        Http::fake([
            'test/disbursement' => Http::response('<?xml version="1.0" encoding="UTF-8"?><response><transactionid>12345</transactionid></response>', 200)
        ]);

        $response = $this->service->disburse('**********89', 1000, 'test-ref');

        $this->assertEquals('12345', $response['transactionid']);
    }

    public function test_it_handles_successful_loan_application()
    {
        Http::fake([
            'test/initiateloanapplication' => Http::response('<?xml version="1.0" encoding="UTF-8"?><response><loanaccount><accountnumber>12345</accountnumber></loanaccount></response>', 200)
        ]);

        $response = $this->service->createLoanApplication('**********89', 1000, 7);

        $this->assertEquals('12345', $response['loanaccount']['accountnumber']);
    }

    public function test_it_handles_successful_customer_details()
    {
        Http::fake([
            'test/getcustomerdetails' => Http::response('<?xml version="1.0" encoding="UTF-8"?><response><status>REGISTERED</status></response>', 200)
        ]);

        $response = $this->service->getCustomerDetails('**********89');

        $this->assertEquals('REGISTERED', $response['status']);
    }

    public function test_it_prevents_weekday_operations()
    {
        // Mock current time to a weekday
        Carbon::setTestNow(Carbon::create(2024, 1, 1)); // Monday

        $this->expectException(MtnApiException::class);
        $this->expectExceptionMessage('Service not available on weekdays');

        new MtnApiService();
    }

    public function test_it_allows_weekend_operations()
    {
        // Mock current time to a weekend
        Carbon::setTestNow(Carbon::create(2024, 1, 6)); // Saturday

        $service = new MtnApiService();
        $this->assertInstanceOf(MtnApiService::class, $service);
    }
}
